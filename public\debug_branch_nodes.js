/**
 * Debug script for Branch Node Condition Isolation
 * 
 * Run this in the browser console on a flow page to test if the branch node
 * condition sharing issue has been resolved.
 */

console.log('🔧 Branch Node Debug Script Loaded');
console.log('=====================================');

// Function to analyze current flow data
function analyzeBranchNodes() {
    console.log('🔍 Analyzing current flow data...');
    
    // Try to get flow data from window
    const flowData = window.data?.flow?.flow_data;
    if (!flowData) {
        console.error('❌ No flow data found in window.data.flow.flow_data');
        return;
    }
    
    let parsedData;
    try {
        parsedData = typeof flowData === 'string' ? JSON.parse(flowData) : flowData;
    } catch (e) {
        console.error('❌ Failed to parse flow data:', e);
        return;
    }
    
    console.log('📊 Flow data parsed successfully');
    console.log('Total nodes:', parsedData.nodes?.length || 0);
    
    // Find all branch nodes
    const branchNodes = parsedData.nodes?.filter(node => node.type === 'branch') || [];
    console.log('🌿 Branch nodes found:', branchNodes.length);
    
    if (branchNodes.length === 0) {
        console.log('ℹ️ No branch nodes found in this flow');
        return;
    }
    
    // Analyze each branch node
    const conditionIdMap = new Map();
    let hasSharedIds = false;
    
    branchNodes.forEach((node, index) => {
        console.log(`\n🌿 Branch Node ${index + 1}:`);
        console.log(`   ID: ${node.id}`);
        
        const conditions = node.data?.settings?.conditions || [];
        console.log(`   Conditions: ${conditions.length}`);
        
        conditions.forEach((condition, condIndex) => {
            console.log(`   Condition ${condIndex + 1}:`);
            console.log(`     ID: ${condition.id}`);
            console.log(`     Variable: ${condition.variableId}`);
            console.log(`     Operator: ${condition.operator}`);
            console.log(`     Value: ${condition.value}`);
            
            // Check for shared IDs
            if (conditionIdMap.has(condition.id)) {
                console.error(`     ⚠️ SHARED ID DETECTED! This condition ID is used by node: ${conditionIdMap.get(condition.id)}`);
                hasSharedIds = true;
            } else {
                conditionIdMap.set(condition.id, node.id);
            }
            
            // Check if condition ID belongs to this node
            if (!condition.id.startsWith(`${node.id}_cond_`)) {
                console.warn(`     ⚠️ Condition ID doesn't belong to this node!`);
                console.warn(`     Expected prefix: ${node.id}_cond_`);
                console.warn(`     Actual ID: ${condition.id}`);
            }
        });
    });
    
    // Summary
    console.log('\n📋 Analysis Summary:');
    console.log(`   Total branch nodes: ${branchNodes.length}`);
    console.log(`   Total conditions: ${Array.from(conditionIdMap.keys()).length}`);
    console.log(`   Shared condition IDs: ${hasSharedIds ? 'YES ❌' : 'NO ✅'}`);
    
    if (hasSharedIds) {
        console.error('❌ ISSUE DETECTED: Branch nodes are sharing condition IDs!');
        console.log('💡 This means the fix is not working properly.');
    } else {
        console.log('✅ SUCCESS: All branch nodes have unique condition IDs!');
    }
    
    return {
        branchNodes,
        conditionIdMap,
        hasSharedIds,
        totalConditions: Array.from(conditionIdMap.keys()).length
    };
}

// Function to test the migration system
function testMigrationSystem() {
    console.log('\n🧪 Testing Migration System...');
    
    // Create test data with shared condition IDs
    const testData = {
        nodes: [
            {
                id: "branch-test-1",
                type: "branch",
                data: {
                    settings: {
                        conditions: [{
                            id: 'shared-condition-id-123',
                            variableId: 'contact_name',
                            operator: 'equals',
                            value: 'test'
                        }]
                    }
                }
            },
            {
                id: "branch-test-2",
                type: "branch",
                data: {
                    settings: {
                        conditions: [{
                            id: 'shared-condition-id-123', // Same ID - should be fixed
                            variableId: 'contact_email',
                            operator: 'contains',
                            value: '@example.com'
                        }]
                    }
                }
            }
        ],
        edges: []
    };
    
    console.log('📝 Created test data with shared condition IDs');
    
    // Check if migration functions are available
    if (typeof window.autoMigrateFlowData === 'function') {
        console.log('✅ Migration function found');
        
        const migratedData = window.autoMigrateFlowData(testData);
        
        // Check if migration worked
        const node1Condition = migratedData.nodes[0].data.settings.conditions[0];
        const node2Condition = migratedData.nodes[1].data.settings.conditions[0];
        
        if (node1Condition.id !== node2Condition.id) {
            console.log('✅ Migration test PASSED: Condition IDs are now unique');
            console.log(`   Node 1 condition ID: ${node1Condition.id}`);
            console.log(`   Node 2 condition ID: ${node2Condition.id}`);
        } else {
            console.error('❌ Migration test FAILED: Condition IDs are still the same');
        }
    } else {
        console.warn('⚠️ Migration function not found in window object');
        console.log('💡 This might mean the migration system is not loaded');
    }
}

// Function to simulate user interaction
function simulateUserInteraction() {
    console.log('\n🎭 Simulating User Interaction...');
    
    // Try to find branch node elements in the DOM
    const branchElements = document.querySelectorAll('[data-testid="rf__node-branch"]');
    console.log(`Found ${branchElements.length} branch node elements in DOM`);
    
    if (branchElements.length === 0) {
        console.log('ℹ️ No branch nodes visible in the current view');
        return;
    }
    
    // Try to find condition input fields
    const conditionInputs = document.querySelectorAll('input[placeholder="Value"]');
    console.log(`Found ${conditionInputs.length} condition value inputs`);
    
    if (conditionInputs.length >= 2) {
        console.log('🧪 Testing condition isolation...');
        
        // Store original values
        const originalValues = Array.from(conditionInputs).map(input => input.value);
        console.log('Original values:', originalValues);
        
        // Modify first input
        const testValue = 'test-' + Date.now();
        conditionInputs[0].value = testValue;
        conditionInputs[0].dispatchEvent(new Event('change', { bubbles: true }));
        
        // Wait a bit and check if other inputs changed
        setTimeout(() => {
            const newValues = Array.from(conditionInputs).map(input => input.value);
            console.log('Values after modification:', newValues);
            
            let isolationWorking = true;
            for (let i = 1; i < newValues.length; i++) {
                if (newValues[i] === testValue) {
                    console.error(`❌ Input ${i + 1} was affected by changes to input 1!`);
                    isolationWorking = false;
                }
            }
            
            if (isolationWorking) {
                console.log('✅ Condition isolation is working correctly!');
            } else {
                console.error('❌ Condition isolation is NOT working!');
            }
            
            // Restore original values
            conditionInputs.forEach((input, index) => {
                input.value = originalValues[index];
                input.dispatchEvent(new Event('change', { bubbles: true }));
            });
        }, 100);
    }
}

// Function to check if fixes are properly loaded
function checkFixesLoaded() {
    console.log('\n🔍 Checking if fixes are properly loaded...');
    
    // Check if migration utilities are available
    const migrationFunctions = [
        'autoMigrateFlowData',
        'validateFlowData',
        'hasSharedConditionIds',
        'fixSharedConditionIds'
    ];
    
    migrationFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} is available`);
        } else {
            console.warn(`⚠️ ${funcName} is NOT available`);
        }
    });
    
    // Check if test functions are available
    if (typeof window.runMigrationTest === 'function') {
        console.log('✅ runMigrationTest is available');
    } else {
        console.warn('⚠️ runMigrationTest is NOT available');
    }
}

// Main debug function
function debugBranchNodes() {
    console.log('🚀 Starting comprehensive branch node debug...');
    console.log('================================================');
    
    checkFixesLoaded();
    const analysis = analyzeBranchNodes();
    testMigrationSystem();
    simulateUserInteraction();
    
    console.log('\n🏁 Debug complete!');
    console.log('================================================');
    
    return analysis;
}

// Export functions to window for easy access
window.debugBranchNodes = debugBranchNodes;
window.analyzeBranchNodes = analyzeBranchNodes;
window.testMigrationSystem = testMigrationSystem;
window.simulateUserInteraction = simulateUserInteraction;
window.checkFixesLoaded = checkFixesLoaded;

console.log('🎯 Debug functions available:');
console.log('   - debugBranchNodes() - Run all tests');
console.log('   - analyzeBranchNodes() - Analyze current flow data');
console.log('   - testMigrationSystem() - Test migration functions');
console.log('   - simulateUserInteraction() - Test UI interaction');
console.log('   - checkFixesLoaded() - Check if fixes are loaded');
console.log('\n💡 Run debugBranchNodes() to start comprehensive testing');
