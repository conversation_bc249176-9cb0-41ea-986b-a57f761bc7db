<section id="features" class="w-full bg-white " >
    <div class="pB-10 mx-auto max-w-7xl md:px-8">

        @php
            // Filter out empty features
            $validFeatures = collect($features)->filter(function($feature) {
                return is_object($feature) && !empty($feature->title) && !empty($feature->image_link);
            })->values();
        @endphp

        @for ($i = 0; $i < count($validFeatures); $i++)
            @php
                $feature = $validFeatures[$i];
            @endphp
            @if ($i ==0 || $i == 3 || $i == 6 || $i == 9 || (($i+1)>count($validFeatures)-1))
                @include('wpboxlanding::landing.partials.fullproduct')
            @else
                @php
                    $feature2 = $validFeatures[$i+1];
                    $i++;
                @endphp
                @include('wpboxlanding::landing.partials.twoproducts')

            @endif
        @endfor
    </div>
</section>