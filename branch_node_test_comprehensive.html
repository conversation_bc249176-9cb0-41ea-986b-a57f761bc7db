<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Node Condition Isolation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section.info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .test-section.success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .test-section.error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .success { color: #4caf50; font-weight: bold; }
        .error { color: #f44336; font-weight: bold; }
        .warning { color: #ff9800; font-weight: bold; }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976d2; }
        .code-block {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .condition-display {
            background: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .node-section {
            border: 2px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .node-section.node1 { border-color: #4caf50; }
        .node-section.node2 { border-color: #2196f3; }
        .node-section.node3 { border-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔧 Branch Node Condition Isolation Test</h1>
    
    <div class="test-section info">
        <h2>Test Overview</h2>
        <p>This comprehensive test verifies that branch node conditions are properly isolated and not sharing references between different branch nodes.</p>
        <p><strong>Issue:</strong> When modifying conditions in one branch node, all other branch nodes were updating with the same values.</p>
        <p><strong>Expected Fix:</strong> Each branch node should maintain its own isolated condition data.</p>
    </div>

    <div class="test-section">
        <h2>🧪 Test Controls</h2>
        <button onclick="runComprehensiveTest()">Run Comprehensive Test</button>
        <button onclick="testMigrationSystem()">Test Migration System</button>
        <button onclick="testRealWorldScenario()">Test Real-World Scenario</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Simulate the problematic flow data structure
        const createTestFlowData = () => ({
            nodes: [
                {
                    id: "branch-0.7439814461370684",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035', // SHARED ID - PROBLEM!
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                },
                {
                    id: "branch-0.8212877124900572",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035', // SAME ID - PROBLEM!
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                },
                {
                    id: "branch-0.7868949944308767",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035', // SAME ID - PROBLEM!
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                }
            ],
            edges: []
        });

        // Migration functions (simplified versions for testing)
        const generateUniqueConditionId = (nodeId) => {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(7);
            return `${nodeId}_cond_${timestamp}_${random}`;
        };

        const hasSharedConditionIds = (flowData) => {
            const conditionIdMap = new Map();
            
            for (const node of flowData.nodes) {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    for (const condition of node.data.settings.conditions) {
                        if (!conditionIdMap.has(condition.id)) {
                            conditionIdMap.set(condition.id, []);
                        }
                        conditionIdMap.get(condition.id).push(node.id);
                    }
                }
            }
            
            for (const [conditionId, nodeIds] of conditionIdMap.entries()) {
                if (nodeIds.length > 1) {
                    return true;
                }
            }
            
            return false;
        };

        const fixSharedConditionIds = (flowData) => {
            console.log('🔧 Starting flow data migration to fix shared condition IDs...');
            
            const fixedNodes = flowData.nodes.map(node => {
                if (node.type !== 'branch' || !node.data.settings?.conditions) {
                    return node;
                }
                
                const fixedConditions = node.data.settings.conditions.map(condition => {
                    const belongsToThisNode = condition.id.startsWith(`${node.id}_cond_`);
                    
                    if (!belongsToThisNode) {
                        const newId = generateUniqueConditionId(node.id);
                        console.log(`🔄 Migrating condition ID: ${condition.id} → ${newId} (Node: ${node.id})`);
                        
                        return {
                            ...condition,
                            id: newId
                        };
                    }
                    
                    return condition;
                });
                
                return {
                    ...node,
                    data: {
                        ...node.data,
                        settings: {
                            ...node.data.settings,
                            conditions: fixedConditions
                        }
                    }
                };
            });
            
            console.log('✅ Flow data migration completed');
            
            return {
                ...flowData,
                nodes: fixedNodes
            };
        };

        function runComprehensiveTest() {
            const results = document.getElementById('results');
            let html = '<div class="test-section"><h2>🧪 Comprehensive Test Results</h2>';
            
            // Test 1: Create problematic data
            html += '<h3>Test 1: Create Problematic Flow Data</h3>';
            const testData = createTestFlowData();
            html += '<div class="condition-display">Created test data with 3 branch nodes sharing the same condition ID</div>';
            
            // Test 2: Detect shared condition IDs
            html += '<h3>Test 2: Detect Shared Condition IDs</h3>';
            const hasShared = hasSharedConditionIds(testData);
            if (hasShared) {
                html += '<div class="success">✅ PASS: Successfully detected shared condition IDs</div>';
            } else {
                html += '<div class="error">❌ FAIL: Failed to detect shared condition IDs</div>';
            }
            
            // Test 3: Fix shared condition IDs
            html += '<h3>Test 3: Fix Shared Condition IDs</h3>';
            const fixedData = fixSharedConditionIds(testData);
            
            // Test 4: Verify fix worked
            html += '<h3>Test 4: Verify Fix Worked</h3>';
            const hasSharedAfterFix = hasSharedConditionIds(fixedData);
            if (!hasSharedAfterFix) {
                html += '<div class="success">✅ PASS: No shared condition IDs after fix</div>';
            } else {
                html += '<div class="error">❌ FAIL: Still has shared condition IDs after fix</div>';
            }
            
            // Test 5: Verify unique IDs per node
            html += '<h3>Test 5: Verify Unique IDs Per Node</h3>';
            let allUnique = true;
            const conditionIds = new Set();
            
            for (const node of fixedData.nodes) {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    for (const condition of node.data.settings.conditions) {
                        if (conditionIds.has(condition.id)) {
                            allUnique = false;
                            break;
                        }
                        conditionIds.add(condition.id);
                        
                        if (!condition.id.startsWith(`${node.id}_cond_`)) {
                            allUnique = false;
                            break;
                        }
                    }
                }
            }
            
            if (allUnique) {
                html += '<div class="success">✅ PASS: All condition IDs are unique and belong to their respective nodes</div>';
            } else {
                html += '<div class="error">❌ FAIL: Some condition IDs are not unique or don\'t belong to their nodes</div>';
            }
            
            // Test 6: Display before and after
            html += '<h3>Test 6: Before vs After Comparison</h3>';
            html += '<div class="node-section node1"><h4>Before Migration:</h4>';
            testData.nodes.forEach((node, index) => {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    html += `<div class="condition-display">Node ${index + 1} (${node.id}): Condition ID = ${node.data.settings.conditions[0].id}</div>`;
                }
            });
            html += '</div>';
            
            html += '<div class="node-section node2"><h4>After Migration:</h4>';
            fixedData.nodes.forEach((node, index) => {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    html += `<div class="condition-display">Node ${index + 1} (${node.id}): Condition ID = ${node.data.settings.conditions[0].id}</div>`;
                }
            });
            html += '</div>';
            
            html += '</div>';
            results.innerHTML = html;
        }

        function testMigrationSystem() {
            const results = document.getElementById('results');
            let html = '<div class="test-section"><h2>🔄 Migration System Test</h2>';
            
            // Test the auto-migration function
            const testData = createTestFlowData();
            
            html += '<h3>Auto-Migration Test</h3>';
            html += '<div class="code-block">Original data has shared condition IDs: ' + hasSharedConditionIds(testData) + '</div>';
            
            const migratedData = fixSharedConditionIds(testData);
            html += '<div class="code-block">After migration has shared condition IDs: ' + hasSharedConditionIds(migratedData) + '</div>';
            
            if (!hasSharedConditionIds(migratedData)) {
                html += '<div class="success">✅ Migration system working correctly!</div>';
            } else {
                html += '<div class="error">❌ Migration system failed!</div>';
            }
            
            html += '</div>';
            results.innerHTML = html;
        }

        function testRealWorldScenario() {
            const results = document.getElementById('results');
            let html = '<div class="test-section"><h2>🌍 Real-World Scenario Test</h2>';
            
            // Simulate the exact scenario from the user's console logs
            const realWorldData = {
                nodes: [
                    {
                        id: "branch-0.7439814461370684",
                        type: "branch",
                        data: {
                            settings: {
                                conditions: [{
                                    id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                    variableId: 'contact_last_message',
                                    operator: 'equals',
                                    value: 'vinee'
                                }]
                            }
                        }
                    },
                    {
                        id: "branch-0.8212877124900572",
                        type: "branch",
                        data: {
                            settings: {
                                conditions: [{
                                    id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                    variableId: 'contact_last_message',
                                    operator: 'equals',
                                    value: 'vinee'
                                }]
                            }
                        }
                    },
                    {
                        id: "branch-0.7868949944308767",
                        type: "branch",
                        data: {
                            settings: {
                                conditions: [{
                                    id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                    variableId: 'contact_last_message',
                                    operator: 'equals',
                                    value: 'vinee'
                                }]
                            }
                        }
                    }
                ],
                edges: []
            };
            
            html += '<h3>Real-World Data Analysis</h3>';
            html += '<div class="condition-display">Testing with actual problematic data from user console logs</div>';
            
            const hasSharedBefore = hasSharedConditionIds(realWorldData);
            html += `<div class="code-block">Has shared condition IDs: ${hasSharedBefore}</div>`;
            
            const fixedRealWorld = fixSharedConditionIds(realWorldData);
            const hasSharedAfter = hasSharedConditionIds(fixedRealWorld);
            html += `<div class="code-block">After fix has shared condition IDs: ${hasSharedAfter}</div>`;
            
            if (hasSharedBefore && !hasSharedAfter) {
                html += '<div class="success">✅ Real-world scenario fixed successfully!</div>';
            } else {
                html += '<div class="error">❌ Real-world scenario fix failed!</div>';
            }
            
            html += '</div>';
            results.innerHTML = html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run a basic test on page load
        window.onload = function() {
            console.log('🔧 Branch Node Test Page Loaded');
            console.log('Available test functions:');
            console.log('- runComprehensiveTest()');
            console.log('- testMigrationSystem()');
            console.log('- testRealWorldScenario()');
        };
    </script>
</body>
</html>
